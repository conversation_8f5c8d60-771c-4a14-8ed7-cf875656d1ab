import { formatNumberToThousands } from "@/utils/core/tools";
import { ENUMCONFIGS } from "@/utils/config/Config";

// ===== TYPES =====

export interface TransactionItem {
  id: string;
  title: string;
  status_name: string;
  right_amount: string;
  payment_method?: string;
  quantity: number;
  amount?: number;
  total_amount?: number;
  recharge_status?: number;
  ship_status?: number;
  audit_status?: number;
  err_msg?: string;
  status_desc?: string;
  update_type?: string;
  created_at: string;
  updated_at?: string;
  pay_channel?: string;

  // 存款相关字段
  recharge_id?: number;
  pay_serial_no?: number;
  fail_type?: number;
  coins?: number;
  back_coins?: number;

  // 提款相关字段
  order_no?: string;
  name?: string;
  type?: number;
  images?: any;
  paid_at?: string;
  ship_fail_code?: number;
  ship_fail_message?: string;

  // 奖励相关字段
  player_id?: number;
  game_code?: string;
}

export interface PageData {
  list: TransactionItem[];
  total_page: number;
}

export interface TabData {
  [key: string]: TransactionItem[];
}

export interface PageListObj {
  Deposit: TabData;
  Withdrawal: TabData;
  Reward: TabData;
}

export interface CurrentPage {
  Deposit: number;
  Withdrawal: number;
  Reward: number;
}

export interface UseTransactionDataOptions {
  initialTab?: TabType;
}

// Component Props
export interface TransactionGroupProps {
  date: string;
  items: TransactionItem[];
  showTopPadding?: boolean;
}

// ===== CONSTANTS =====

export const TABS = ["Deposit", "Withdrawal", "Reward"] as const;
export type TabType = (typeof TABS)[number];

// Transaction Types
export const TRANRECORD_TYPE = {
  ADJUSTMENT: "Adjustment",
  MAYAPAY: "mayapay",
  MAYAWEBPAY: "mayawebpay",
  GCASHWEBPAY: "gcashwebpay",
} as const;

export const RECORD_TYPE = {
  DEPOSIT: "Deposit",
  WITHDRAWAL: "Withdrawal",
  REWARD: "Reward",
  TRANSFER: "Transfer",
} as const;

// Status Mappings
export const STATUS_MAP = {
  All: "",
  Successful: 1,
  Pending: 2,
  Unsuccessful: 3,
} as const;

export const WITHDRAWAL_STATUS_MAP = {
  All: "",
  Successful: 2,
  Pending: 1,
  Unsuccessful: 3,
} as const;

export const SELECT_STATUS_OPTIONS = [
  { label: "All", value: "" },
  { label: "Pending", value: 1 },
  { label: "Successful", value: 2 },
  { label: "Unsuccessful", value: 3 },
] as const;

export const RECHARGE_STATUS_OPTIONS = ["All", "Successful", "Pending", "Unsuccessful"] as const;

export const STATUS_COLORS = {
  1: "#11BE6B",
  2: "#FFA500",
  3: "#FF4848",
  Unsuccessful: "#FF4848",
  Successful: "#11BE6B",
  Pending: "#FFA500",
} as const;

// Payment Methods
export const PAYMENT_METHODS = {
  GCASH: "Gcash",
  MAYA: "Maya",
} as const;

// Page Configuration
export const PAGE_CONFIG = {
  PAGE_SIZE: 15,
  DATE_TYPE: "3",
  INITIAL_PAGE: 1,
  EMPTY_DATA_TEXT: "No Record",
  LOADING_TEXT: "Loading...",
} as const;

// Detailed Status Mappings
export const DEPOSIT_STATUS_MAP = {
  SUCCESSFUL: "1",
  PENDING: ["2", "5", "6", "7", "8"],
  UNSUCCESSFUL: "3",
} as const;

export const WITHDRAWAL_AUDIT_STATUS = {
  PENDING: ["1", "2", "4", "5"],
  UNSUCCESSFUL: "3",
} as const;

export const WITHDRAWAL_SHIP_STATUS = {
  PENDING: "1",
  SUCCESSFUL: "2",
  UNSUCCESSFUL: "3",
} as const;

// ===== UTILITY FUNCTIONS =====

/**
 * Merge grouped data for pagination
 */
export const mergeGroupedData = (oldData: TabData, newData: TabData): TabData => {
  if (!oldData || !newData) return { ...oldData, ...newData };

  const oldKeys = Object.keys(oldData);
  const newKeys = Object.keys(newData);

  if (oldKeys.length === 0) return newData;
  if (newKeys.length === 0) return oldData;

  const lastOldKey = oldKeys[oldKeys.length - 1];
  const firstNewKey = newKeys[0];

  // If last old date equals first new date, merge the data
  if (lastOldKey === firstNewKey) {
    return {
      ...oldData,
      [lastOldKey]: [...(oldData[lastOldKey] || []), ...(newData[firstNewKey] || [])],
      ...Object.fromEntries(newKeys.slice(1).map((k) => [k, newData[k]])),
    };
  }

  return { ...oldData, ...newData };
};

/**
 * Type guard for valid numbers
 */
export const isValidNumber = (value: any): value is number => {
  return typeof value === "number" && !isNaN(value) && isFinite(value);
};

/**
 * Process payment method identifier
 */
export const processPaymentMethod = (paymentMethod?: string): string => {
  if (!paymentMethod) return "";

  const method = paymentMethod.toLowerCase();
  if (method.includes("gcash")) return PAYMENT_METHODS.GCASH;
  if (method.includes("maya")) return PAYMENT_METHODS.MAYA;

  return "";
};

/**
 * Format amount display with error handling
 */
export const formatAmount = (
  amount: number | undefined,
  totalAmount: number | undefined,
  isWithdrawal: boolean
): string => {
  try {
    const value = isValidNumber(amount) ? amount : isValidNumber(totalAmount) ? totalAmount : 0;
    const absValue = Math.abs(value) / 100;
    const sign = isWithdrawal || value < 0 ? "-" : "+";

    const formattedValue = formatNumberToThousands(absValue);
    return `${sign}${formattedValue}`;
  } catch (error) {
    console.error("Error formatting amount:", error, { amount, totalAmount, isWithdrawal });
    return isWithdrawal ? "-0.00" : "+0.00";
  }
};

/**
 * Process deposit status with error handling
 */
export const processDepositStatus = (item: TransactionItem): void => {
  try {
    const rechargeStatus = String(item.recharge_status ?? "");

    if (rechargeStatus === DEPOSIT_STATUS_MAP.SUCCESSFUL) {
      item.status_name = "Successful";
    } else if (DEPOSIT_STATUS_MAP.PENDING.includes(rechargeStatus as any)) {
      item.status_name = "Pending";
    } else if (rechargeStatus === DEPOSIT_STATUS_MAP.UNSUCCESSFUL) {
      item.status_name = item.err_msg || "Unsuccessful";
    } else {
      item.status_name = "Pending";
    }
  } catch (error) {
    console.error("Error processing deposit status:", error, item);
    item.status_name = "Error";
  }
};

/**
 * Process withdrawal status with error handling
 */
export const processWithdrawalStatus = (item: TransactionItem): void => {
  try {
    item.status_name = item.status_desc || "";
    item.recharge_status = item.ship_status;
    item.amount = item.total_amount;

    const auditStatus = String(item.audit_status ?? "");
    const shipStatus = String(item.ship_status ?? "");

    if (shipStatus === WITHDRAWAL_SHIP_STATUS.UNSUCCESSFUL) {
      item.status_name = item.err_msg || "Unsuccessful";
    } else if (shipStatus === WITHDRAWAL_SHIP_STATUS.SUCCESSFUL) {
      item.status_name = "Successful";
    } else if (shipStatus === WITHDRAWAL_SHIP_STATUS.PENDING) {
      if (WITHDRAWAL_AUDIT_STATUS.PENDING.includes(auditStatus as any)) {
        item.status_name = "Pending";
      } else if (auditStatus === WITHDRAWAL_AUDIT_STATUS.UNSUCCESSFUL) {
        item.status_name = item.err_msg || "Unsuccessful";
      } else {
        item.status_name = "Pending";
      }
    } else {
      item.status_name = "Pending";
    }
  } catch (error) {
    console.error("Error processing withdrawal status:", error, item);
    item.status_name = "Error";
  }
};

/**
 * Process reward status with error handling
 */
export const processRewardStatus = (item: TransactionItem, enumList: any[] = []): void => {
  try {
    if (!Array.isArray(enumList)) {
      console.warn("Invalid enumList provided:", enumList);
      enumList = [];
    }

    const enumItem = enumList.find((e) => e?.change_type === item.update_type);
    const configItem = ENUMCONFIGS.find((p) => p?.type === item.update_type);

    item.status_name = enumItem?.title || configItem?.title || "Reward";
  } catch (error) {
    console.error("Error processing reward status:", error, item);
    item.status_name = "Reward";
  }
};

/**
 * Main transaction status processor with error handling
 */
export function processTransactionStatus(
  item: TransactionItem,
  tab: TabType,
  enumList: any[] = []
): TransactionItem {
  try {
    // Defensive programming: ensure required fields exist
    if (!item || typeof item !== "object") {
      console.warn("Invalid transaction item:", item);
      return item;
    }

    // Set basic information
    item.title = item.pay_channel === TRANRECORD_TYPE.ADJUSTMENT ? "Transfer" : tab;
    item.payment_method = processPaymentMethod(item.payment_method);
    item.right_amount = formatAmount(item.amount, item.total_amount, tab === "Withdrawal");

    // Process status based on transaction type
    switch (tab) {
      case "Deposit":
        processDepositStatus(item);
        break;
      case "Withdrawal":
        processWithdrawalStatus(item);
        break;
      case "Reward":
        processRewardStatus(item, enumList);
        break;
      default:
        console.warn(`Unknown transaction tab: ${tab}`);
        item.status_name = "Unknown";
    }

    return item;
  } catch (error) {
    console.error("Error processing transaction status:", error, item);
    // Return safe object with default values
    return {
      ...item,
      title: tab,
      status_name: "Error",
      right_amount: "0.00",
      payment_method: "",
    };
  }
}

// ==================== 向后兼容 ====================
// 保持原有的导出名称以避免破坏现有代码
export const tabs = TABS;
