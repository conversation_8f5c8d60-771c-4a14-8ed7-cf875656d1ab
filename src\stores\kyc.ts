import { defineStore } from "pinia";
import { getYearsoldWithDate, stringToInt } from "@/utils/core/tools";
import { KYC_STEP } from "@/views/kyc/CONSTANT";
import { getGlobalDialog } from "@/enter/vant";
import router from "@/router";
import { updateInfo } from "@/api/user";
import { KycMgr } from "@/utils/KycMgr";

// 详版表单初始值
const initDetailFormData = {
  first_name: "",
  middle_name: "",
  last_name: "",
  day: "",
  month: "",
  year: "",
  branch: "",
  nationality: "",
  place_of_birth: "",
  current_address: "",
  permanent_address: "",
  work: "",
  income: "",
  country: "Philippines",
  id_type: "",
  account_type: "",
  account_no: "",
};
// 简版表单初始值
const initSimpleFormData = {
  first_name: "",
  middle_name: "",
  last_name: "",
  day: "",
  month: "",
  year: "",
};

export const useKycStore = defineStore("kyc", {
  state: () => ({
    detailFormData: { ...initDetailFormData }, // 详版表单
    simpleFormData: { ...initSimpleFormData }, // 简版表单
    detailDayInputErrTip: "", // 详版-出生年月错误提示
    simpleDayInputErrTip: "", // 简版-出生年月错误提示
    isSameCurrentAddress: true, // 详版-原先地址和当前地址一致
    isGovemmentOfficial: true, // 详版-政府官员
    curStep: KYC_STEP.KYC_STEP_NAME, // 详版-当前步骤
    detailPhotoBase64: "", // 详版-照片base64
    simplePhotoBase64: "", // 简版-照片base64
    showPhoneChangeDialog: false, // kyc 手机号绑定
  }),
  actions: {
    // 上一步
    handlePreStep() {
      const $dialog = getGlobalDialog();
      switch (this.curStep) {
        case KYC_STEP.KYC_STEP_ADDRESS:
          this.curStep = KYC_STEP.KYC_STEP_NAME;
          break;
        case KYC_STEP.KYC_STEP_PHOTO:
          this.curStep = KYC_STEP.KYC_STEP_ADDRESS;
          break;
        case KYC_STEP.KYC_STEP_MEDIA:
          this.curStep = KYC_STEP.KYC_STEP_PHOTO;
          break;
        case KYC_STEP.KYC_STEP_NAME:
          $dialog({
            title: "Tips",
            message: "Are you sure you want to cancel your registration?",
            confirmText: "Confirm",
            cancelText: "Cancel",
            onConfirm: async () => {
              router.replace("/");
            },
          });
          break;
        default:
          break;
      }
    },
    handleSimplePre() {
      const $dialog = getGlobalDialog();
      $dialog({
        title: "Tips",
        message: "Are you sure you want to cancel your registration?",
        confirmText: "Confirm",
        cancelText: "Cancel",
        onConfirm: async () => {
          router.back();
        },
      });
    },
    // 下一步
    handleNextStep() {
      switch (this.curStep) {
        case KYC_STEP.KYC_STEP_NAME:
          this.curStep = KYC_STEP.KYC_STEP_ADDRESS;
          break;
        case KYC_STEP.KYC_STEP_ADDRESS:
          this.curStep = KYC_STEP.KYC_STEP_PHOTO;
          break;
        case KYC_STEP.KYC_STEP_PHOTO:
          this.curStep = KYC_STEP.KYC_STEP_MEDIA;
          break;
        case KYC_STEP.KYC_STEP_MEDIA:
          this.handleDetailSubmit();
          break;
        default:
          break;
      }
    },
    // 更新详版照片
    updateDetailPhotoBase64(value: string) {
      this.detailPhotoBase64 = value;
    },
    // 更新简版照片
    updateSimplePhotoBase64(value: string) {
      this.simplePhotoBase64 = value;
    },
    // 同步当前地址到原先地址
    handleSameAddressChecked() {
      this.detailFormData.permanent_address = this.detailFormData.current_address;
    },
    // 下拉选择赋值
    handleSelectConfirm(field: keyof typeof initDetailFormData, data: { value: string }) {
      if (field) {
        this.detailFormData[field] = data.value;
      }
    },
    // 详版表单字段更新
    updateDetailFormData(field: keyof typeof initDetailFormData, value: any) {
      if (typeof this.detailFormData !== "object" || this.detailFormData === null) {
        this.detailFormData = { ...initDetailFormData };
      }
      let errStr = this.vaildDateInputErr(field, value);
      if (
        !errStr &&
        getYearsoldWithDate(
          this.detailFormData.year,
          this.detailFormData.month,
          this.detailFormData.day
        ) < 21
      ) {
        errStr = "Under 21 years old";
      }
      this.detailDayInputErrTip = errStr;
      this.detailFormData[field] = value;
      // 同步当前地址到原先地址
      if (field === "current_address" && this.isSameCurrentAddress) {
        this.detailFormData.permanent_address = this.detailFormData.current_address;
      }
    },
    // 简版表单字段更新
    updateSimpleFormData(field: keyof typeof initSimpleFormData, value: any) {
      if (typeof this.simpleFormData !== "object" || this.simpleFormData === null) {
        this.simpleFormData = { ...initSimpleFormData };
      }
      let errStr = this.vaildDateInputErr(field, value);
      if (
        !errStr &&
        getYearsoldWithDate(
          this.simpleFormData.year,
          this.simpleFormData.month,
          this.simpleFormData.day
        ) < 21
      ) {
        errStr = "Under 21 years old";
      }
      this.simpleDayInputErrTip = errStr;
      this.simpleFormData[field] = value;
    },
    // 校验日期输入
    vaildDateInputErr(field: string, value: any) {
      let errStr = "";
      if (["day", "month", "year"].includes(field)) {
        if (!value) {
          errStr = "Date of Birth cannot be empty";
        }
      }
      if (field === "day") {
        const day = stringToInt(value);
        if (day < 1 || day > 31) {
          errStr = "Please enter a correct date";
        }
      }
      if (field === "month") {
        const month = stringToInt(value);
        if (month < 1 || month > 12) {
          errStr = "Please enter a correct month";
        }
      }
      if (field === "year") {
        const year = stringToInt(value);
        const nowYear = new Date().getFullYear();
        if (year < 1920 || year > nowYear) {
          errStr = "Please enter a correct year";
        }
      }
      return errStr;
    },
    // 详版表单提交
    async handleDetailSubmit() {
      await updateInfo({
        ...this.detailFormData,
        font_side_url: this.detailPhotoBase64,
      });

      // 更新kyc状态
      KycMgr.instance.kycState = 2;
      KycMgr.instance.refreshKycState();

      // reset data
      this.curStep = KYC_STEP.KYC_STEP_NAME;
      this.detailFormData = { ...initDetailFormData };
      this.detailPhotoBase64 = "";
      this.detailDayInputErrTip = "";
      this.isSameCurrentAddress = true;
      this.isGovemmentOfficial = true;

      router.replace("/kyc/success");
    },
    // 简版表单提交
    async handleSimpleSubmit() {
      await updateInfo({
        ...this.simpleFormData,
        font_side_url: this.simplePhotoBase64,
      });
      // 更新状态
      KycMgr.instance.kycState = 2;
      KycMgr.instance.refreshKycState();

      // reset data
      this.simpleFormData = { ...initSimpleFormData };
      this.simplePhotoBase64 = "";
      this.simpleDayInputErrTip = "";

      router.replace("/kyc/success?simple=1");
    },
  },
  persist: {
    key: "keyConfigStore",
    storage: window.localStorage,
  },
});
