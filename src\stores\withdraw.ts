import { defineStore } from "pinia";
import {
  getWithdrawAccounts,
  goExchange,
  checkWithdrawRisk,
  confirmWithdrawPassword,
} from "@/api/withdrawal";
import { rechargeWithdraw } from "@/api/deposit";
import { useGlobalStore } from "@/stores/global";
import { showToast } from "vant";
import { CHANEL_TYPE, PRODUCT_TYPE, MAINTENANCETIPCODE } from "@/utils/config/GlobalConstant";
import { GeetestMgr, GEETEST_TYPE } from "@/utils/GeetestMgr";
import router from "@/router";
import { Md5 } from "@/utils/core/Md5";
import { getGlobalDialog } from "@/enter/vant";
import { KycMgr, InGameType } from "@/utils/KycMgr";
import { maskAccountNumber } from "@/utils/core/tools";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";
import enTranslations from "@/utils/I18n/en.json";
import { METHODS_NAMES, METHODS_ID } from "@/utils/config/GlobalConstant";

// 风控信息接口
export interface RiskWithdrawalInfo {
  status: number;
  user_id: string;
  user_total_valid_bet: number;
  target_amount: number;
}

// 提现数据接口
interface WithdrawData {
  min: number;
  max: number;
  sort: number;
  name: string;
}

// 账户信息接口
interface AccountInfo {
  account_id?: string;
  account_no?: string;
  name?: string;
  type?: number;
}

export const useWithdrawStore = defineStore("withdraw", {
  state: () => {
    return {
      // 已选中的账户信息
      selectedAccountInfo: null as AccountInfo | null,
      //弹窗：显示提现弹
      showWithdrawDialog: false,
      //弹窗：显示账户选择列表
      showAccountListDialog: false,
      //弹窗：小程序内金额确认
      showMoneyConfirmDialog: false,
      // 弹窗：1倍流水风控制
      showRiskWithdrawalDialog: false,
      // 弹窗：设置支付密码设置前置
      showPreconditionsVerifyDialog: false,
      // 弹窗：设置支付密码
      showPaymentPasswordInputDialog: false,
      // 弹窗：密码次数过多限制
      showPaymentPassworLimtDialog: false,
      // 账户列表
      accounts: [] as AccountInfo[],
      // 提现方式列表
      withdrawData: [] as WithdrawData[],
      // 当前提现方式的可提的最大、最小值
      minAmountNum: null as number | null,
      maxAmountNum: null as number | null,
      // 输入（选中）的充值金额
      selectedAmount: "",
      // 当前银行卡归宿，Maya、Gcash
      curRechangeMethods: "",
      // 错误提示
      errTip: "",
      // 警告提示
      warnTip: "",
      // 1倍流水风控信息
      riskWithdrawalInfo: null as RiskWithdrawalInfo | null,
      // 次数限制 email
      customerEmail: "",
    };
  },
  getters: {
    // 渠道来源
    CHANEL_TYPE() {
      const globalStore = useGlobalStore();
      return globalStore.channel;
    },
    // 是否小程序端，即 G_CASH、MAYA 端
    isMiniChannel() {
      const globalStore = useGlobalStore();
      const channel = globalStore.channel?.toLowerCase();
      return ["gcash", "maya"].includes(channel);
    },
    $dialog() {
      return getGlobalDialog();
    },
  },
  actions: {
    // 打开提现入口
    async openDialog() {
      const globalStore = useGlobalStore();
      if (!globalStore.token) {
        router.push("/login");
        return;
      }
      // 显示加载状态
      showZLoading();
      try {
        // 初始化数据
        await this.loadAmountConfig();
        if (!this.isMiniChannel) {
          const accountResponse = await getWithdrawAccounts();
          if (accountResponse.list && accountResponse.list.length > 0) {
            //有提现账户跳转提现页面
            this.handleRiskWithdrawalDialog(() => {
              this.showWithdrawDialog = true;
            });
          } else {
            // 无提现账户 直接跳转添加提现账户页面
            this.handleRiskWithdrawalDialog(() => {
              router.push("/account/withdraw-account");
            });
          }
        } else if (this.CHANEL_TYPE === CHANEL_TYPE.G_CASH) {
          this.handleRiskWithdrawalDialog(() => {
            this.showWithdrawDialog = true;
          });
        } else if (this.CHANEL_TYPE === CHANEL_TYPE.MAYA) {
          this.handleRiskWithdrawalDialog(() => {
            this.showWithdrawDialog = true;
          });
        }
      } finally {
        // 关闭加载状态
        closeZLoading();
      }
    },
    // 输入框 input事件
    handleCustomAmountInput(event: Event) {
      const input = event.target as HTMLInputElement;
      let value = input.value.replace(/\D/g, "") as string;

      // 如果输入为空，直接处理
      if (!value) {
        this.setSelectedAmount("");
        this.getVaildAmountErr("");
        return;
      }

      // 金额限制：确保不超过最大限额
      const numValue = Number(value);
      if (this.maxAmountNum && this.maxAmountNum > 0 && numValue > this.maxAmountNum) {
        value = this.maxAmountNum.toString();
        // 更新输入框显示值
        input.value = value;
      }

      this.setSelectedAmount(value);
    },
    // 点击选中
    setSelectedAmount(amount: string) {
      this.selectedAmount = amount;
      this.getVaildAmountErr(amount);
    },
    // 校验金额范围提示
    getVaildAmountErr(value: string | number) {
      const min = this.minAmountNum || 0;
      const max = this.maxAmountNum || 0;
      const globalStore = useGlobalStore();
      const availableBalance = globalStore.balance;
      const numValue = Number(value);

      // 重置提示状态
      this.errTip = "";
      this.warnTip = "";

      // 空值处理
      if (value === "" || value === null || value === undefined) {
        this.errTip = `Enter Amount ${min} - ${max}₱`;
        return this.errTip;
      }

      // 无效数值处理
      if (isNaN(numValue) || numValue < 0) {
        this.errTip = "Please enter a valid amount";
        return this.errTip;
      }

      // 余额不足检查（优先级最高）
      if (availableBalance < numValue) {
        this.errTip = "Insufficient Balance.";
        return this.errTip;
      }

      // 金额范围验证
      if (numValue < min) {
        this.errTip = `The minimum amount is ${min}₱`;
      } else if (numValue > max) {
        this.errTip = `The maximum amount is ${max}₱`;
      } else if (numValue === max) {
        // 达到最大限额时显示警告
        this.warnTip = `The maximum allowable input is ${max}₱`;
      }

      return this.errTip;
    },
    // 选择银行账户
    handleCardCheck(item: AccountInfo) {
      this.selectedAccountInfo = item;
      this.showAccountListDialog = false;
      const index = this.withdrawData.findIndex((it) => it.account_type === item.type);
      this.setWithdrawRangeValue(index);
      this.curRechangeMethods = METHODS_NAMES[item.type as keyof typeof METHODS_NAMES];
    },
    // 设置提现的最大、最小范围
    setWithdrawRangeValue(index: number) {
      this.minAmountNum = this.withdrawData[index]?.min || null;
      this.maxAmountNum = this.withdrawData[index]?.max || null;
    },
    // 获取提现方式 及金额配置
    async getWithdrawConfigList(callBack?: () => void) {
      const res = await rechargeWithdraw({
        appChannel: this.CHANEL_TYPE,
      });
      if (res && res.withdraw) {
        this.withdrawData = res.withdraw;
        for (let index = 0; index < this.withdrawData.length; index++) {
          this.withdrawData.sort((a, b) => {
            if (a.sort != b.sort) {
              //按sort值从大到小
              return b.sort - a.sort;
            } else {
              //sort值相同按照首字母从大到小排序
              if (a.name && b.name) {
                return b.name.localeCompare(a.name);
              }
              return 0;
            }
          });
        }
        callBack && callBack();
      }
      return this.withdrawData;
    },
    async loadAmountConfig() {
      try {
        const globalStore = useGlobalStore();
        await this.getWithdrawConfigList(() => {
          this.setWithdrawRangeValue(0);
        });

        if (this.isMiniChannel) {
          // 小程序端
          this.selectedAccountInfo = {
            account_no: maskAccountNumber(globalStore.userInfo.phone || ""),
            name: globalStore.channel,
            type: METHODS_ID[globalStore.channel as keyof typeof METHODS_ID],
          };
          this.curRechangeMethods = globalStore.channel;
        } else if (CHANEL_TYPE.WEB === this.CHANEL_TYPE) {
          const accountResponse = await getWithdrawAccounts();
          const accountData = accountResponse.data || accountResponse;
          const list = accountData.list || [];
          this.accounts = list;
          if (list && list.length) {
            // 默认选中第一个吧
            this.selectedAccountInfo = {
              ...list[0],
              name: METHODS_NAMES[list[0].type],
            };
            this.curRechangeMethods = METHODS_NAMES[list[0].type];
          }
        }
      } catch (error) {
        console.error("Load withdraw config failed:", error);
        this.withdrawData = [];
        this.accounts = [];
        this.showWithdrawDialog = false;
        showToast("The current network is abnormal, please try again later.");
      } finally {
      }
    },

    // 1倍流水风险校验
    async handleRiskWithdrawalDialog(callBack: () => void, jumpCheckRisk = false) {
      if (jumpCheckRisk) {
        callBack && callBack();
      } else {
        const response = await checkWithdrawRisk({});
        const { code, data } = response;
        if (code === 200 || code === 0) {
          if (data.status === 0) {
            this.riskWithdrawalInfo = data;
            this.showRiskWithdrawalDialog = true;
            // Got bet 跳历史游戏？
          } else {
            callBack && callBack();
          }
        } else {
          callBack && callBack();
        }
      }
    },

    // 提现底部确认入口
    async handleConfirm() {
      try {
        // 小程序没有添加账号，只有当前账号
        if (this.isMiniChannel || this.accounts.length) {
          // 1倍流水风险校验
          await this.handleRiskWithdrawalDialog(() => this.preVaildFn());
        } else {
          this.showWithdrawDialog = false;
          router.push(`/account/withdraw-account`);
        }
      } catch (error) {
        console.error("Withdraw risk check failed:", error);
        this.$dialog({
          title: "Tips",
          message: "Failed to verify withdrawal conditions. Please try again.",
          confirmText: "Done",
          showCancelButton: false,
          onConfirm: () => {
            this.showWithdrawDialog = false;
          },
        });
      }
    },
    // 前置校验
    preVaildFn() {
      if (this.isMiniChannel) {
        // 二次确认弹窗
        this.showMoneyConfirmDialog = true;
      } else if (this.CHANEL_TYPE === CHANEL_TYPE.WEB) {
        //先验证一下kyc ，回调执行以下代码 614行
        KycMgr.instance.verifyKyc(InGameType.Withdraw, (isVerity) => {
          if (isVerity) {
            // 这里是 认证过之后的 逻辑 其他逻辑会自动 执行
            const globalStore = useGlobalStore();
            const userInfo = globalStore.userInfo;
            if (userInfo.withdraw_password === 0) {
              // 理论上这个条件不会执行，因为前置拦截了，必须是有提现密码的
              this.showWithdrawDialog = false;
              this.showPreconditionsVerifyDialog = true;
            } else {
              // 输入支付密码
              this.showPaymentPasswordInputDialog = true;
            }
          } else {
            this.showWithdrawDialog = false;
          }
        });
      }
    },
    // 支付密码确认
    async handleCompletePayPwd(paymentPassword: string) {
      try {
        const response = await confirmWithdrawPassword({
          withdraw_password: Md5.hashStr(paymentPassword)?.toString() || "",
        });
        const { code, customer_email, msg } = response;

        if (code === 200 || code === 0) {
          this.showPaymentPasswordInputDialog = false;
          this.reqExchange();
        } else {
          //输入次数达到上限
          if (code === 102008) {
            this.showWithdrawDialog = false;
            this.showPaymentPasswordInputDialog = false;
            this.showPaymentPassworLimtDialog = true;
            this.customerEmail = customer_email || "";
          } else if (code === 102004) {
            msg && showToast(msg);
          } else {
            msg && showToast(msg);
          }
        }
      } catch (error) {
        console.error("Payment password verification failed:", error);
        showToast("Network error. Please try again.");
      }
    },
    // 密码次数过多限制，复制email
    handlePasswordLimtConfirm() {
      this.showPaymentPassworLimtDialog = false;
    },
    // 绑定手机号码、设置支付密码设置成功
    handlePasswordSuccConfirm() {
      this.showPaymentPasswordInputDialog = false;
      // 关闭弹窗、让其二次发起吧
      // this.reqExchange();
    },
    // 小程序端提现弹窗 确认
    async handleMoneyConfirm() {
      this.showMoneyConfirmDialog = false;
      this.reqExchange();
    },
    // 极验
    reqExchange() {
      GeetestMgr.instance.geetest_device(GEETEST_TYPE.withdraw, (succ: any) => {
        if (succ) {
          let ret = {};
          if (Object.getPrototypeOf(succ) !== Boolean.prototype) {
            ret = succ;
          }
          this.reqExchange_true(ret);
        }
      });
    },
    // 发起提现请求
    async reqExchange_true(ret?: any) {
      try {
        const globalStore = useGlobalStore();
        const params = {
          account_id: (this.selectedAccountInfo as any)?.type || "",
          product_type: PRODUCT_TYPE.MAYA_MINI,
          name: globalStore.userInfo.nickname,
          amount: parseInt(this.selectedAmount),
          price: parseInt(this.selectedAmount),
          quantity: 1,
          geetest_guard: ret?.geetest_guard || "",
          userInfo: ret?.userInfo || "",
          geetest_captcha: ret?.geetest_captcha || "",
          buds: ret?.buds || "64",
        };

        if (this.CHANEL_TYPE == CHANEL_TYPE.WEB) {
          if ((this.selectedAccountInfo as any)?.type == METHODS_ID.Maya) {
            params["product_type"] = PRODUCT_TYPE.WITHDRAW_MAYA_WEB;
          } else if ((this.selectedAccountInfo as any)?.type == METHODS_ID.Gcash) {
            params["product_type"] = PRODUCT_TYPE.WITHDRAW_GCASH_WEB;
          }
        }

        if (this.CHANEL_TYPE == CHANEL_TYPE.G_CASH) {
          params["product_type"] = PRODUCT_TYPE.GCASH;
          params["app_package_name"] = "com.nustargame.gcash";
        }

        const response = await goExchange(params);
        const { code, msg } = response;

        if (code === 200 || code === 0) {
          this.showWithdrawDialog = false;
          this.$dialog({
            title: "Congratulations",
            message:
              "Your withdrawal request has been submitted successfully. You will receive in your account within 10 minutes.",
            confirmText: "Done",
            showCancelButton: false,
            onConfirm: async () => {
              // 更新余额
              globalStore.getBalance();
            },
          });
        } else {
          if (code == MAINTENANCETIPCODE) {
            // 服务器维护页
            this.showWithdrawDialog = false;
            router.push("/system/maintenance");
          } else if (code == 1) {
            this.$dialog({
              title: "Tips",
              message: msg || "Some mistakes occurred.",
              confirmText: "Done",
              showCancelButton: false,
              onConfirm: async () => {},
            });
            this.showWithdrawDialog = false;
          } else {
            this.$dialog({
              title: "Tips",
              message: msg || "Some mistakes occurred.",
              confirmText: "Done",
              showCancelButton: false,
              onConfirm: async () => {},
            });
            this.showWithdrawDialog = false;
          }
        }
      } catch (error) {
        console.error("Withdrawal request failed:", error);
        this.showWithdrawDialog = false;
        this.$dialog({
          title: "Tips",
          message: "Network error occurred. Please check your connection and try again.",
          confirmText: "Done",
          showCancelButton: false,
          onConfirm: () => {
            // 可以选择重新打开弹窗或其他操作
          },
        });
      }
    },

    /**拉取不到数据或用户未配置/未选择提现账号 点击按钮给出提示信息 */
    handleInputClick() {
      if (!this.withdrawData || this.withdrawData.length === 0) {
        showToast({
          message: enTranslations.tipword101,
          className: "custom-toast-width",
        });
        return;
      }
      if (!this.accounts || this.accounts.length === 0) {
        showToast({
          message: enTranslations.tipword99,
        });
        return;
      }
      let filter_list = this.withdrawData.filter((item) => {
        return item.account_type == this.selectedAccountInfo.type;
      });
      if (filter_list.length == 0) {
        let tips_msg = enTranslations.tipword102;
        if (this.CHANEL_TYPE == CHANEL_TYPE.G_CASH) {
          tips_msg = enTranslations.tipword103;
        }
        showToast({
          message: tips_msg,
        });
        return;
      }
    },

    resetData() {
      this.selectedAmount = "";
      this.errTip = "";
    },
  },
});
