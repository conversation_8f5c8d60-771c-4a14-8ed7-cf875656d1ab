import ScrollNumber from "../component/ScrollNumber";
import { E_PAGE_TYPE, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import { AutoPopMgr } from "../mgr/AutoPopMgr";
import { uiManager } from "../mgr/UIManager";
import HttpUtils from "../net/HttpUtils";
import utils from "../utils/utils";
import Big from "../libs/big.js";
import GameControl from "../GameControl";

const {ccclass, property} = cc._decorator;

export function showSpinPop() {
    let parent = Global.getInstance().popNode
    if (!parent) return
    let gameNd = parent.getChildByName("SpinView")
    if (gameNd) {
        gameNd.active = true;
        return
    }

    uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.SpinView, cc.Prefab, () => {
    }, (err, prefab: any) => {
        if (!prefab) return
        let gameNd = parent.getChildByName("SpinView");
        if (gameNd) {
            return
        }
        if (err) {
            return;
        }
        if(Global.getInstance().getPageId() != E_PAGE_TYPE.HALL){
            AutoPopMgr.set_isshowing();//没弹出来后卡住问题
            return
        }
        let node = cc.instantiate(prefab);
        node.name = "SpinView"
        node.zIndex = 1;
        node.position = new cc.Vec3(0,0);
        node.parent = Global.getInstance().popNode;
        let currentDate = new Date();
        let currentDateStr = `${currentDate.getFullYear()}-${currentDate.getMonth() + 1}-${currentDate.getDate()}`;
        if(!!Global.getInstance().token){
            cc.sys.localStorage.setItem("SPIN_LAST_ENTER_TIME"+Global.getInstance().userdata.user_id, currentDateStr);
        }
    });
}

@ccclass
export default class SpinWheel extends cc.Component {

    @property(cc.Node)
    nodePandi: cc.Node = null;

    @property(cc.Node)
    nodeZhen: cc.Node = null;

    @property(cc.Node)
    awardHistroyView: cc.Node = null;

    @property(cc.Node)
    awardHistroyViewModel: cc.Node = null;

    @property(cc.Button)
    btnSpin: cc.Button = null;

    @property([cc.SpriteFrame])
    btnSpriteFrame: cc.SpriteFrame[] = [];

    //这里是11个小灯 闪烁使用
    @property([cc.Node])
    runSpriteNode: cc.Node[] = [];

    @property([cc.SpriteFrame])
    titleSpriteFrame: cc.SpriteFrame[] = [];

    @property(cc.ProgressBar)
    nextProgress: cc.ProgressBar = null;

    @property(cc.Node)
    finger: cc.Node = null;

    _running = false;
    animTotalTime = 0;
    currentAwardArr = null;

    runHouseActionSlow = null; 
    runHouseActionFast = null; 

    lastTotalNum = 0;
    allSpinActivityInfo = null;

    panStatus = 0;
    panTips = "";
    activityEnded = false;
    lastShowTotalPrize = 0;
    isFirstMode = true;
    animAwardIndex = 0;
    lastReqSpinInfoTime = 0;//间隔请求

    wheelName = ["Bronze Spin","Silver Spin","Gold Spin","Diamond Spin","Super Spin"];

    type = null;
    onLoad () {
        window['SpinWheel'] = true;
        this.lastReqSpinInfoTime = Global.getInstance().now();
    }

    initType(args){
        this.type = args;
    }

    start () {
        let runhouse = utils.getChildByPath(this.node,"bg.pan_up_bg");
        this.runHouseActionSlow = cc.tween(runhouse).repeatForever(
            cc.tween()
            .delay(0.5)
            .call(()=>{
                this.lights_flex(true)
            })
            .delay(0.5)
            .call(()=>{
                this.lights_flex()
            })
        );
        this.runHouseActionFast = cc.tween(runhouse).repeatForever(
            cc.tween()
            .delay(0.2)
            .call(()=>{
                this.lights_flex(true)
            })
            .delay(0.2)
            .call(()=>{
                this.lights_flex()
            })
        );
        this.runHouseActionSlow.start();

        cc.tween(this.finger).repeatForever(
            cc.tween()
            .to(0.3,{scale:1.1})
            .to(0.3,{scale:0.9})
        ).start();

        this.getSpinData();
        this.getOtherPrizeList();
    }

    lights_flex(is_sighle = false){
        for (let index = 0; index < this.runSpriteNode.length; index++) {
            const element = this.runSpriteNode[index];
            if(is_sighle){
                if(index % 2 == 0){
                    element.active = false
                }else{
                    element.active = true
                }
            }else{
                if(index % 2 != 0){
                    element.active = false
                }else{
                    element.active = true
                }
            }
        }
    }
    protected onEnable(): void {
       //这里控制一下 获取数据的 间隔
       let gap = (Global.getInstance().now() - this.lastReqSpinInfoTime)/1000;
        if (gap < 10) {
            return;
        }
        this.lastReqSpinInfoTime = Global.getInstance().now()
        this.getSpinData();
    }
    getSpinData() {
        HttpUtils.getInstance().get(1, 0, this, "/open/api/activity/spin/config/receive_info", {
            token: Global.getInstance().token,
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if (response.data) {
                this.initWheel(response.data);
            }
            this.popEndTips(response);
        },(response)=>{
            this.popEndTips(response);
        });
    }

    initWheel(msg) {
        msg.spin_activity.sort((a,b)=> a.sort- b.sort);
        this.allSpinActivityInfo = msg.spin_activity;
        this.resetBottons();
        let lab_progress = this.nextProgress.node.getChildByName("lab_progress").getComponent(cc.Label);
        let currentId = msg.current_spin_activity_sort;
        let spin_activity = msg.spin_activity;
        this.currentAwardArr = spin_activity[currentId]//this.getNextSortByActivityInfo(currentId,msg);
        if(currentId >= 5) {
            this.currentAwardArr = spin_activity[4];
        }

        for (let index = 0; index <spin_activity.length; index++) {
            let element = spin_activity[index];
            let pzconfig = element.spin_activity_prize_config;
            pzconfig.sort((a,b)=> a.sort- b.sort);
            let number = utils.getChildByPath(this.node,"bottom.btn_level_"+(index+1)+".number");
            number.getComponent(cc.Label).string = "₱"+ utils.formatNumberWithCommas(pzconfig[4].prize,0);
        }

        for (let index = 0; index <spin_activity.length; index++) {
            let element = spin_activity[index];
            let pzconfig = element.spin_activity_prize_config;
            if (element.sort == this.currentAwardArr.sort) { //所有转盘还没领完的状态
                let btn = utils.getChildByPath(this.node,"bottom.btn_level_"+(index+1));
                btn.color = cc.color(255,255,255);
                btn.getChildByName('number').color = cc.color(255,255,255);
                let sptitle = utils.getChildByPath(this.node,"bg.title.sptitle").getComponent(cc.Sprite);
                sptitle.spriteFrame = this.titleSpriteFrame[currentId];
                if(currentId >= 5) {
                    sptitle.spriteFrame = this.titleSpriteFrame[4];
                }

                //初始化转盘上的奖励金额
                pzconfig.sort((a,b)=> a.sort- b.sort);
                for (let k = 0; k < pzconfig.length; k++) {
                    let pzem = pzconfig[k];
                    let pzlab =  utils.getChildByPath(this.node,"bg.pan.lab_award_"+(k+1)).getComponent(cc.Label);
                    pzlab.string = "₱"+ utils.formatNumberWithCommas(pzem.prize,0);
                }
                let gap = new Big(parseFloat(element.daily_betting_amount)).minus(msg.bet_amount).toString();
                if (msg.bet_amount < element.daily_betting_amount) {
                    let nextName = spin_activity[index].spin_activity_name;
                    this.nextProgress.progress = parseFloat(msg.bet_amount)/parseFloat(element.daily_betting_amount);
                    lab_progress.string = "Bet ₱" + utils.formatNumberWithCommas(gap,0) + " to start " + nextName + "!";
                    this.btnSpin.getComponent(cc.Sprite).spriteFrame = this.btnSpriteFrame[1];
                    this.finger.active = false;
                    this.panStatus = 0;
                    this.panTips = "Bet ₱" + utils.formatNumberWithCommas(gap,0) + " to start " + nextName + "!";
                } else {
                    this.nextProgress.progress = 1;
                    this.btnSpin.getComponent(cc.Sprite).spriteFrame = this.btnSpriteFrame[0];
                    this.finger.active = true;
                    lab_progress.string = "Please click the spin to start " + spin_activity[index].spin_activity_name + "!";
                    if (msg.bet_amount >= spin_activity[4].daily_betting_amount) {
                        lab_progress.string = "All the spins have been obtained, to use them!";
                    }
                    this.panStatus = 1;
                    Global.getInstance().spinInfo.real_left_times = 1;
                    if (currentId >= 5) {
                        this.panStatus = 2;
                        Global.getInstance().spinInfo.real_left_times = 0;
                        this.btnSpin.getComponent(cc.Sprite).spriteFrame = this.btnSpriteFrame[1];
                        this.finger.active = false;
                        lab_progress.string = "Spins all used up today. Try your luck again tomorrow!";
                        this.panTips = lab_progress.string;
                    }
                }
                break;
            }
        }
        
        if (!msg.is_first_recharge) {
            this.panStatus = 3;
            this.nextProgress.progress = 0;
            this.btnSpin.getComponent(cc.Sprite).spriteFrame = this.btnSpriteFrame[1];
            this.finger.active = false;
            lab_progress.string = "Unlock this Spin by making First Deposit.";
            this.panTips = lab_progress.string;
        }
    }

    resetBottons() {
        for (let index = 1; index < 6; index++) {
            let btn = utils.getChildByPath(this.node,"bottom.btn_level_"+index);
            btn.color = cc.color(164,164,164);
        }
    }

    runPan() {
        if (this._running) {
            return;
        }
        this._running = true;

        if (this.panStatus != 1) {
            if (this.panTips != "") {
                Global.getInstance().showSimpleTip(this.panTips);
            } else {
                Global.getInstance().showSimpleTip("Failed to obtain information.");
            }
            this._running = false;
            return;
        }

        this.btnSpin.enabled = false;
        this.finger.active = false;
        HttpUtils.getInstance().post(1, 0, this, "/open/api/activity/spin/prize", {
            token: Global.getInstance().token,
            spin_activity_id:this.currentAwardArr.spin_activity_id
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                this._running = false;
                return;
            }
            if (response.data && response.data.spin_activity_prize_id) {
                this.currentAwardArr.spin_activity_prize_config.sort((a,b)=>{return a.sort - b.sort});
                for (let index = 0; index < this.currentAwardArr.spin_activity_prize_config.length; index++) {
                    const element = this.currentAwardArr.spin_activity_prize_config[index];
                    if (element.spin_activity_prize_config_id == response.data.spin_activity_prize_id) {
                        this._doRun(element.sort);
                    }
                }
            } else {
                this.btnSpin.enabled = true;
                this._running = false;
            }
            this.popEndTips(response);
        },(response)=>{
            this.btnSpin.enabled = true;
            this._running = false;
            this.popEndTips(response);
        });
    }
    
    getOtherPrizeList() {
        HttpUtils.getInstance().get(1, 0, this, "/open/api/activity/spin/config/receive_record/platform", {
            token: Global.getInstance().token,
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if (response.data && response.data.list && response.data.list.length > 0) {
                if (response.data.list.length > 2) {
                    this.showAwardPlayer(response.data.list);
                } else {
                    this.showTempList2(response.data.list);
                }
            } else {
                this.showTempList();
            }
            this.showTotalAnim(response);
        },(response)=>{
            
        });
    }

    _doRun(stopId) {
        if (!stopId || stopId <= 0 || stopId > 5) {
            return;
        }
        this.runHouseActionSlow.stop();
        this.runHouseActionFast.start();
        let totalCount = 5
        let roundCountMin = 4 //转动最小圈数
        let roundCountMax = 6 //转动最大圈数
        let singleAngle = 360 / totalCount
        let panOffset = 0
        let offsetAngle = 5
        let angleMin = (stopId-1) * singleAngle
        let roundCount = this.getRandomInt(roundCountMin,roundCountMax);
        let angleTotal = -360 * roundCount + angleMin - panOffset  - this.getRandomFloat(-singleAngle/2 + offsetAngle, singleAngle/2 - offsetAngle) + 72;
        this.nodePandi.angle = -72;

        let targetShan = this.nodePandi.getChildByName("shan_"+stopId);
        targetShan.stopAllActions();
        let run_time = 6

        // cc.tween(this.nodeZhen).delay(0.25).to(0.07,{angle:30}).to(2,{angle:0},{easing:"elasticOut"}).start();

        let index = 0.5;

        let foreverAciton = cc.tween(this.node).repeatForever(
            cc.tween()
                .delay(0.01)
                .call(() => {
                    let raw = Math.floor(this.nodePandi.angle);
                    if (raw < -singleAngle * index) {
                        index += 1;
                        cc.tween(this.nodeZhen).to(0.07, { angle: 30 }).to(2, { angle: 0 }, { easing: "elasticOut" }).start();
                    }
            }));
        foreverAciton.start();

        cc.tween(this.nodePandi)
            .by(run_time/6,{angle:angleTotal/6},{easing:"backIn"})
            .by(run_time*5/6,{angle:angleTotal*5/6},{easing:"expoOut"})
            .call(()=>{
                foreverAciton.stop();
                targetShan.active = true;
                targetShan.runAction(cc.repeatForever(cc.sequence(
                    cc.fadeTo(0.5, 0),
                    cc.fadeTo(0.5, 255))))
                this._running = false;
                this.btnSpin.enabled = true;
                this.runHouseActionSlow.start();
                this.runHouseActionFast.stop();
                cc.director.emit("update_spin_time",true)
            })
            .delay(2)
            .call(()=>{
                this.popAward(stopId);
                this.getSpinData();
            })
            .start();
    }

    popAward(idx) {
        let award_view = this.node.getChildByName("award_view");
        award_view.active = true;
        let prize_amount = this.currentAwardArr.spin_activity_prize_config[idx-1].prize;
        let labaward = utils.getChildByPath(this.node,"award_view.bg.lab_award").getComponent(cc.Label);
        if (prize_amount) {
            labaward.string = "₱"+ utils.formatNumberWithCommas(prize_amount,0);
        }
    }

    //修改逻辑为 去下注
    hideAwardView() {
        let award_view = this.node.getChildByName("award_view");
        //首先隐藏自己 
        award_view.active = false;
        Global.getInstance().updateBalanceAfterGame();
        this.resetUi();
        // 再隐藏转盘 
        this.clickClose();
        // 再跳转到游戏逻辑
        let gamescene =  cc.director.getScene().getComponentInChildren(GameControl);
        gamescene.mTabbar.clickGoBet();
    }

    createAwardPlayerNode(element,list) {
        let node = cc.instantiate(this.awardHistroyViewModel);
        let lab_name = node.getChildByName("lab_name").getComponent(cc.Label);
        let lab_award = node.getChildByName("lab_award").getComponent(cc.Label);
        let lab_time = node.getChildByName("lab_time").getComponent(cc.Label);
        lab_name.string = element.user_id;
        lab_award.string = "₱" + utils.formatNumberWithCommas(parseInt(element.prize_amount),0);
        lab_time.string =  element.prize_time;
        node.parent = this.awardHistroyView;
        node.x = 0;

        if (this.animAwardIndex <= 1 && this.isFirstMode) {
            cc.tween(node)
                .call(()=>{
                    let anim = node.getComponent(cc.Animation);
                    anim.play("bulletAward",2-this.animAwardIndex);
                })
                .call(()=>{
                    this.animAwardIndex++;
                    this.createAwardPlayerNode(list[this.animAwardIndex],list);
                })
                .delay(2)
                .call(()=>{
                    node.destroy();
                })
                .start();
        } else {
            this.isFirstMode = false;
            cc.tween(node)
                .call(()=>{
                    let anim = node.getComponent(cc.Animation);
                    anim.play("bulletAward");
                })
                .delay(1)
                .call(()=>{
                    if (list.length-1 == this.animAwardIndex ) {
                        this.getOtherPrizeList();
                        this.animAwardIndex = 0;
                    } else {
                        this.animAwardIndex++;
                        this.createAwardPlayerNode(list[this.animAwardIndex],list);
                    }
                })
                .delay(2.33)
                .call(()=>{
                    node.destroy();
                })
                .start();
        }
    }

    showAwardPlayer(msg) {
        let award_history1 = this.awardHistroyView.getChildByName("award_history1");
        let award_history2 = this.awardHistroyView.getChildByName("award_history2");
        award_history1.active = false;
        award_history2.active = false;
        this.createAwardPlayerNode(msg[0],msg);
        // let total = (msg.length+this.animTotalTime)*1000;
        // setTimeout(() => {
        //     this.animTotalTime = 0;
        //     this.getOtherPrizeList();
        // }, total);
    }

    showTempList() {
        let award_history1 = this.awardHistroyView.getChildByName("award_history1");
        let award_history2 = this.awardHistroyView.getChildByName("award_history2");
        award_history1.active = true;
        award_history2.active = true;

        let currentDate = new Date();
        let currentDateStr = `${currentDate.getFullYear()}-${currentDate.getMonth() + 1}-${currentDate.getDate()}`;
        let name1 = Global.getInstance().getStoreageData("SPIN_TEMP_ITEM_NAME1","");
        let time1 = Global.getInstance().getStoreageData("SPIN_TEMP_ITEM_TIME1","");
        let name2 = Global.getInstance().getStoreageData("SPIN_TEMP_ITEM_NAME2","");
        let time2 = Global.getInstance().getStoreageData("SPIN_TEMP_ITEM_TIME2","");

        let lab_name1 = award_history1.getChildByName("lab_name").getComponent(cc.Label);
        let lab_award1 = award_history1.getChildByName("lab_award").getComponent(cc.Label);
        let lab_time1 = award_history1.getChildByName("lab_time").getComponent(cc.Label);
        let lab_name2 = award_history2.getChildByName("lab_name").getComponent(cc.Label);
        let lab_award2 = award_history2.getChildByName("lab_award").getComponent(cc.Label);
        let lab_time2 = award_history2.getChildByName("lab_time").getComponent(cc.Label);

        lab_award1.string = "₱500";
        lab_award2.string = "₱100";

        let tpname = name1.split("_");
        if (tpname && tpname[0] && tpname[1] && currentDateStr == tpname[0]) {
            lab_name1.string = tpname[1];
        } else {
            lab_name1.string = "30***"+ this.getRandomInt(100,990);
            Global.getInstance().setStoreageData("SPIN_TEMP_ITEM_NAME1",currentDateStr+"_"+lab_name1.string);
        }

        let tptime = time1.split("_");
        if (tptime && tptime[0] && tptime[1] && currentDateStr == tptime[0]) {
            lab_time1.string = tptime[1];
        } else {
            lab_time1.string = "00:" + this.getRandomInt(10,59);
            Global.getInstance().setStoreageData("SPIN_TEMP_ITEM_TIME1",currentDateStr+"_"+lab_time1.string);
        }

        let tpname2 = name2.split("_");
        if (tpname2 && tpname2[0] && tpname2[1] && currentDateStr == tpname2[0]) {
            lab_name2.string = tpname2[1];
        } else {
            lab_name2.string = "31***"+ this.getRandomInt(100,990);
            Global.getInstance().setStoreageData("SPIN_TEMP_ITEM_NAME2",currentDateStr+"_"+lab_name2.string);
        }

        let tptime2 = time2.split("_");
        if (tptime2 && tptime2[0] && tptime2[1] && currentDateStr == tptime2[0]) {
            lab_time2.string = tptime2[1];
        } else {
            lab_time2.string = "00:" + this.getRandomInt(10,59);
            Global.getInstance().setStoreageData("SPIN_TEMP_ITEM_TIME2",currentDateStr+"_"+lab_time2.string);
        }

    }

    showTempList2(msg) {
        for (let index = 0; index < msg.length; index++) {
            let element = msg[index];
            let node = this.awardHistroyView.getChildByName("award_history"+(index+1));
            let lab_name = node.getChildByName("lab_name").getComponent(cc.Label);
            let lab_award = node.getChildByName("lab_award").getComponent(cc.Label);
            let lab_time = node.getChildByName("lab_time").getComponent(cc.Label);
            lab_name.string = element.user_id;
            lab_award.string = "₱" + element.prize_amount;
            lab_time.string =  element.prize_time;
            node.active = true;
        }
    }

    getRandomInt(min: number, max: number): number {
        min = Math.ceil(min);
        max = Math.floor(max);
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    getRandomFloat(min: number, max: number): number {
        return Math.random() * (max - min) + min;
    }

    getNextSortByActivityInfo(activityId,info) {
        if (!info || activityId == null || activityId == undefined) {
            return;
        }
        let curSort = 0;
        for (let index = 0; index < info.spin_activity.length; index++) {
            const element = info.spin_activity[index];
            if (element.sort == activityId){
                curSort = element.sort;
            }
        }
        if (curSort > 0) {
            return info.spin_activity[curSort-1];
        }
        return [];
    }

    showTotalAnim(msg) {
        let lab_total_award = utils.getChildByPath(this.node,"bg.totoalprize.lab_total_award").getComponent(cc.Label);
        if (this.lastShowTotalPrize != 0) {
            return;
        }
        this.lastShowTotalPrize = Global.getInstance().now();
        if (msg.data && msg.data.total_prize >= 0) {
            let targetNum = parseInt(msg.data.total_prize);
            let lastNumber = Math.floor(targetNum);
            let lastReq = Global.getInstance().now();
            this.unscheduleAllCallbacks();
            setTimeout(() => {
                lab_total_award.string = "₱";
                this.showNumberScroll(Math.floor(targetNum),0,true);
                this.schedule(()=>{
                    targetNum = lastNumber + (Global.getInstance().now()-lastReq)/1000*this.getRandomInt(15,25);
                    this.showNumberScroll(Math.floor(targetNum),Math.floor(lastNumber));
                    lastNumber = Math.floor(targetNum);
                    lastReq = Global.getInstance().now();
                },3,cc.macro.REPEAT_FOREVER,0)
            }, 100);
        } else {
            lab_total_award.string = "--";
        }
    }

    showNumberScroll(targetNum,lastNumber,isfirst?) {
        let oldchars: string[] = utils.formatNumberWithCommas(Math.round(lastNumber),0).split("");
        let chars: string[] = utils.formatNumberWithCommas(Math.round(targetNum),0).split("");
        chars = chars.reverse();
        oldchars = oldchars.reverse();

        for (let index = 0; index < chars.length; index++) {
            let element = chars[index];
            let element2 = oldchars[index];
            let labNumber = utils.getChildByPath(this.node,"bg.totoalprize.scrollnumber"+(index+1));
            if (labNumber && labNumber.isValid) {
                labNumber.active = true;
                if (isfirst) {
                    if (parseInt(element) >= 0) {
                        labNumber.getComponent(ScrollNumber).curNum = parseInt(element);
                        labNumber.getComponent(ScrollNumber).setLabel(0,9);
                    }
                } else {
                    if (parseInt(element) >= 0) {
                        if (parseInt(element2) == null || (parseInt(element2) == parseInt(element) && index > 3)) {
                            
                        } else {
                            setTimeout(() => {
                                labNumber.getComponent(ScrollNumber).scrollTo(parseInt(element));
                            }, index*100);
                        }
                    }
                }
            }
        }
    }

    clickClose() {
        if (this._running) {
            Global.getInstance().showSimpleTip("Please wait...");
            return;
        }

        if(this.type == "banner"){
            let gamescene =  cc.director.getScene().getComponentInChildren(GameControl);
            gamescene.mTabbar.rein_home();
            this.node.destroy();
        }else{
            cc.director.emit("DestroyQueuePopup");
            // this.node.destroy();
            this.node.active = false;
        }
    }

    clickDetails() {
        let spin = this.node.getChildByName("SpinDetails");
        if (spin && spin.isValid) {
            return;
        }

        uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.SpinDetial, cc.Prefab, () => {
        }, (err, prefab: any) => {
            if (err) {
                return;
            }
            let node = cc.instantiate(prefab);
            node.name = "SpinDetails"
            node.position = new cc.Vec3(0,0);
            node.parent = this.node;
        });
    }

    clickLevelBtn(event, userdata) {
        if (this.allSpinActivityInfo && parseInt(userdata) && this.allSpinActivityInfo[parseInt(userdata)-1]) {
            let number = this.allSpinActivityInfo[parseInt(userdata)-1].daily_betting_amount;
            let str = "Bet ₱" + utils.formatNumberWithCommas(parseInt(number),0) + " More To Start " + this.wheelName[userdata-1];
            Global.getInstance().showSimpleTip(str);
        }
    }

    onDestroy() {
        cc.director.emit("ReturnToHome2");
        this.unscheduleAllCallbacks();
    }

    popEndTips(msg) {
        if (msg && msg.code) {
            if (msg.code == 500100900 && !this.activityEnded) {
                this.activityEnded = true;
                Global.getInstance().showCommonTip2({ word: "The activity has ended!", confirm: "Done" }, this, true, () => {
                    cc.director.emit("update_spin_time",true);
                    this.activityEnded = false;
                    this.node.destroy();
                });
            }
            if (msg.code != 500100900 && msg.code != 0) {
                Global.getInstance().showSimpleTip("Failed to obtain information.");
            }
        }
    }

    resetUi() {
        for (let index = 0; index < 5; index++) {
            let targetShan = this.nodePandi.getChildByName("shan_"+(index+1));
            targetShan.active = false;
            targetShan.stopAllActions();
        }
        this.nodePandi.angle = -72;
    }
}
